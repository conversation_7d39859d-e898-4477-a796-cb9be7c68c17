<?php

namespace App\Jobs;

use App\Services\ExportStatusService;
use Filament\Actions\Exports\Models\Export;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ProcessExport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 3600; // 1 hour timeout
    public $tries = 3;

    public function __construct(
        public Export $export,
        public string $exporterClass,
        public Builder $query
    ) {}

    public function handle(): void
    {
        try {
            Log::info("Starting export processing", [
                'export_id' => $this->export->id,
                'exporter' => $this->exporterClass,
                'total_rows' => $this->export->total_rows,
            ]);

            // Mark export as started
            ExportStatusService::markAsStarted($this->export->id);

            // Create exporter instance
            $exporter = new $this->exporterClass();
            
            // Process the export with progress tracking
            $this->processExportWithProgress($exporter);

            Log::info("Export processing completed", [
                'export_id' => $this->export->id,
                'successful_rows' => $this->export->successful_rows,
            ]);

        } catch (\Exception $e) {
            Log::error("Export processing failed", [
                'export_id' => $this->export->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            ExportStatusService::markAsFailed(
                $this->export->id,
                'Export failed: ' . $e->getMessage()
            );

            throw $e;
        }
    }

    protected function processExportWithProgress($exporter): void
    {
        $chunkSize = 1000;
        $processedRows = 0;
        $csvData = [];
        
        // Add CSV headers
        $columns = $exporter::getColumns();
        $headers = array_map(fn($column) => $column->getLabel(), $columns);
        $csvData[] = $headers;

        // Process data in chunks
        $this->query->chunk($chunkSize, function ($records) use (&$processedRows, &$csvData, $columns) {
            foreach ($records as $record) {
                $row = [];
                foreach ($columns as $column) {
                    $value = $this->getColumnValue($column, $record);
                    $row[] = $this->formatCsvValue($value);
                }
                $csvData[] = $row;
                $processedRows++;

                // Update progress every 100 records
                if ($processedRows % 100 === 0) {
                    ExportStatusService::updateProgress(
                        $this->export->id,
                        $processedRows,
                        "Processing records... {$processedRows} completed"
                    );
                }
            }
        });

        // Generate CSV content
        $csvContent = $this->generateCsvContent($csvData);
        
        // Save file
        $fileName = $this->generateFileName();
        Storage::disk($this->export->file_disk)->put($fileName, $csvContent);
        
        // Update export record
        $this->export->update([
            'file_name' => $fileName,
            'successful_rows' => $processedRows,
            'processed_rows' => $processedRows,
            'completed_at' => now(),
        ]);

        // Final progress update
        ExportStatusService::updateProgress(
            $this->export->id,
            $processedRows,
            'Export completed successfully'
        );
    }

    protected function getColumnValue($column, $record)
    {
        if ($column->getStateUsing) {
            return $column->evaluate($column->getStateUsing, ['record' => $record]);
        }

        if ($column->getFormatStateUsing) {
            $state = data_get($record, $column->getName());
            return $column->evaluate($column->getFormatStateUsing, ['state' => $state]);
        }

        return data_get($record, $column->getName());
    }

    protected function formatCsvValue($value): string
    {
        if (is_null($value)) {
            return '';
        }

        if (is_bool($value)) {
            return $value ? 'Yes' : 'No';
        }

        if (is_array($value) || is_object($value)) {
            return json_encode($value);
        }

        return (string) $value;
    }

    protected function generateCsvContent(array $data): string
    {
        $output = fopen('php://temp', 'r+');
        
        foreach ($data as $row) {
            fputcsv($output, $row);
        }
        
        rewind($output);
        $csvContent = stream_get_contents($output);
        fclose($output);
        
        return $csvContent;
    }

    protected function generateFileName(): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $exporterName = class_basename($this->exporterClass);
        
        return "exports/{$exporterName}_{$timestamp}_{$this->export->id}.csv";
    }

    public function failed(\Throwable $exception): void
    {
        Log::error("Export job failed completely", [
            'export_id' => $this->export->id,
            'error' => $exception->getMessage(),
        ]);

        ExportStatusService::markAsFailed(
            $this->export->id,
            'Export job failed: ' . $exception->getMessage()
        );
    }
}
