<?php

namespace App\Livewire;

use App\Services\ExportStatusService;
use Livewire\Component;
use Livewire\Attributes\On;

class ExportStatusTracker extends Component
{
    public $activeExports = [];
    public $showTracker = false;
    public $pollingInterval = 2000; // 2 seconds

    public function mount()
    {
        $this->loadActiveExports();
    }

    public function loadActiveExports()
    {
        $exports = ExportStatusService::getActiveExports();
        $this->activeExports = $exports->map(function ($status) {
            return [
                'id' => $status->id,
                'export_id' => $status->export_id,
                'status' => $status->status,
                'progress_percentage' => $status->progress_percentage,
                'processed_rows' => $status->processed_rows,
                'total_rows' => $status->total_rows,
                'status_message' => $status->status_message,
                'estimated_completion' => $status->getEstimatedCompletion(),
                'formatted_progress' => $status->getFormattedProgress(),
                'created_at' => $status->created_at->format('H:i:s'),
            ];
        })->toArray();

        $this->showTracker = count($this->activeExports) > 0;

        // Dispatch update to frontend
        $this->dispatch('export-status-updated', [
            'activeExports' => $this->activeExports,
            'showTracker' => $this->showTracker,
        ]);
    }

    #[On('export-started')]
    public function onExportStarted($exportId)
    {
        $this->loadActiveExports();
        
        // Start polling if not already active
        if ($this->showTracker) {
            $this->dispatch('start-polling');
        }
    }

    #[On('export-completed')]
    public function onExportCompleted($exportId)
    {
        $this->loadActiveExports();
        
        // Stop polling if no active exports
        if (!$this->showTracker) {
            $this->dispatch('stop-polling');
        }
    }

    public function dismissExport($exportId)
    {
        // Remove from active exports array
        $this->activeExports = array_filter($this->activeExports, function ($export) use ($exportId) {
            return $export['export_id'] !== $exportId;
        });

        $this->showTracker = count($this->activeExports) > 0;

        if (!$this->showTracker) {
            $this->dispatch('stop-polling');
        }
    }

    public function render()
    {
        return view('livewire.export-status-tracker');
    }
}
