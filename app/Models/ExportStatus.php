<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ExportStatus extends Model
{
    use HasFactory;

    protected $table = 'export_status';

    protected $fillable = [
        'export_id',
        'user_id',
        'status',
        'progress_percentage',
        'processed_rows',
        'total_rows',
        'status_message',
        'metadata',
        'started_at',
        'completed_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'progress_percentage' => 'integer',
        'processed_rows' => 'integer',
        'total_rows' => 'integer',
    ];

    public function export(): BelongsTo
    {
        return $this->belongsTo(\Filament\Actions\Exports\Models\Export::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if export is in progress
     */
    public function isInProgress(): bool
    {
        return in_array($this->status, ['pending', 'processing']);
    }

    /**
     * Check if export is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if export has failed
     */
    public function hasFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Get progress percentage as formatted string
     */
    public function getFormattedProgress(): string
    {
        return $this->progress_percentage . '%';
    }

    /**
     * Get estimated completion time based on current progress
     */
    public function getEstimatedCompletion(): ?string
    {
        if (!$this->started_at || $this->progress_percentage <= 0) {
            return null;
        }

        $elapsed = now()->diffInSeconds($this->started_at);
        $estimatedTotal = ($elapsed / $this->progress_percentage) * 100;
        $remaining = $estimatedTotal - $elapsed;

        if ($remaining <= 0) {
            return 'Almost done...';
        }

        if ($remaining < 60) {
            return 'Less than 1 minute remaining';
        }

        $minutes = round($remaining / 60);
        return "About {$minutes} minute" . ($minutes > 1 ? 's' : '') . ' remaining';
    }

    /**
     * Update progress with automatic status management
     */
    public function updateProgress(int $processedRows, ?string $message = null): void
    {
        $progressPercentage = $this->total_rows > 0 
            ? min(100, round(($processedRows / $this->total_rows) * 100))
            : 0;

        $this->update([
            'processed_rows' => $processedRows,
            'progress_percentage' => $progressPercentage,
            'status_message' => $message ?? $this->status_message,
            'status' => $progressPercentage >= 100 ? 'completed' : 'processing',
            'completed_at' => $progressPercentage >= 100 ? now() : null,
        ]);
    }

    /**
     * Mark export as failed
     */
    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => 'failed',
            'status_message' => $errorMessage,
            'completed_at' => now(),
        ]);
    }

    /**
     * Mark export as started
     */
    public function markAsStarted(): void
    {
        $this->update([
            'status' => 'processing',
            'started_at' => now(),
            'status_message' => 'Export processing started...',
        ]);
    }
}
