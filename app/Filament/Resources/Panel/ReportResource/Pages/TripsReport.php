<?php

namespace App\Filament\Resources\Panel\ReportResource\Pages;

use App\Filament\Exports\TripsReportExporter;
use App\Filament\Resources\Panel\ReportResource;
use App\Models\Trip;
use App\Services\ExportStatusService;
use App\Traits\CalculatesRevenue;
use Carbon\Carbon;
use Filament\Actions\ExportAction;
use Filament\Actions\Exports\Enums\ExportFormat;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action as FormAction;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Page;
use Illuminate\Database\Eloquent\Builder;

class TripsReport extends Page implements HasForms
{
    use CalculatesRevenue, InteractsWithForms;

    protected static string $resource = ReportResource::class;

    protected static string $view = 'filament.resources.report-resource.pages.trips-report';

    protected static ?string $title = 'Trips Report';

    protected static ?string $navigationLabel = 'Trips Report';

    public ?array $data = [];

    public ?array $reportData = null;

    public bool $reportGenerated = false;

    public function mount(): void
    {
        $this->data = [
            'period' => 'total',
            'date_from' => now()->startOfMonth(),
            'date_to' => now()->endOfMonth(),
        ];

        $this->form->fill($this->data);

        // Generate initial report
        $this->generateReport();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Report Filters')
                    ->schema([
                        Select::make('period')
                            ->label('Time Period')
                            ->options([
                                'total' => 'Total (All Time)',
                                'weekly' => 'This Week',
                                'monthly' => 'This Month',
                                'custom' => 'Custom Range',
                            ])
                            ->default('total')
                            ->required()
                            ->native(false)

                            ->live()
                            ->afterStateUpdated(function ($state, callable $set) {
                                if ($state === 'weekly') {
                                    $set('date_from', now()->startOfWeek());
                                    $set('date_to', now()->endOfWeek());
                                } elseif ($state === 'monthly') {
                                    $set('date_from', now()->startOfMonth());
                                    $set('date_to', now()->endOfMonth());
                                } elseif ($state === 'total') {
                                    $set('date_from', null);
                                    $set('date_to', null);
                                }
                            }),

                        DatePicker::make('date_from')
                            ->label('From Date')
                            ->native(false)
                            ->visible(fn ($get) => in_array($get('period'), ['custom', 'weekly', 'monthly']))
                            ->disabled(fn ($get) => in_array($get('period'), ['weekly', 'monthly']))
                            ->required(fn ($get) => $get('period') === 'custom')
                            ->rules([
                                fn ($get) => function (string $_, $value, \Closure $fail) use ($get) {
                                    if ($get('period') === 'custom' && $get('date_to') && $value && Carbon::parse($value)->isAfter(Carbon::parse($get('date_to')))) {
                                        $fail('The from date must be before or equal to the to date.');
                                    }
                                },
                            ]),

                        DatePicker::make('date_to')
                            ->label('To Date')
                            ->native(false)
                            ->visible(fn ($get) => in_array($get('period'), ['custom', 'weekly', 'monthly']))
                            ->disabled(fn ($get) => in_array($get('period'), ['weekly', 'monthly']))
                            ->required(fn ($get) => $get('period') === 'custom')
                            ->rules([
                                fn ($get) => function (string $_, $value, \Closure $fail) use ($get) {
                                    if ($get('period') === 'custom' && $get('date_from') && $value && Carbon::parse($value)->isBefore(Carbon::parse($get('date_from')))) {
                                        $fail('The to date must be after or equal to the from date.');
                                    }
                                },
                            ]),

                        Actions::make([
                            FormAction::make('generate_report')
                                ->label('Generate Report')
                                ->icon('heroicon-o-chart-bar')
                                ->color('primary')
                                ->action('generateReport')
                                ->size('lg'),
                        ])
                            ->columnSpanFull()
                            ->alignment('center'),
                    ])
                    ->columns(3),
            ])
            ->statePath('data');
    }

    public function getHeaderActions(): array
    {
        return [
            ExportAction::make()
                ->formats([
                    ExportFormat::Csv,
                ])
                ->exporter(TripsReportExporter::class)
                ->label('Export CSV')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                // ->loadingIndicator()
                // ->loadingIndicatorLabel('Preparing export...')
                ->modifyQueryUsing(fn (Builder $query) => $this->getFilteredQuery($query))
                ->after(function ($export) {
                    // Create export status tracking
                    $totalRows = $this->getFilteredQuery(Trip::query())->count();
                    ExportStatusService::createStatus($export, $totalRows);

                    // Dispatch event to start tracking
                    $this->dispatch('export-started', exportId: $export->id);

                    // Show initial notification
                    Notification::make()
                        ->title('Export Started')
                        ->body("CSV export has been queued. Processing {$totalRows} records...")
                        ->info()
                        ->persistent()
                        ->send();
                }),
        ];
    }

    public function generateReport(): void
    {
        try {
            // Validate the form first
            $this->form->validate();

            // Get the form data
            $data = $this->form->getState();

            // Generate the report data
            $this->reportData = $this->calculateTripsData($data);
            $this->reportGenerated = true;

            Notification::make()
                ->title('Report Generated Successfully')
                ->success()
                ->send();

        } catch (\Illuminate\Validation\ValidationException $e) {
            // Re-throw validation exceptions to show form errors
            throw $e;
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error Generating Report')
                ->body('An error occurred while generating the report. Please try again.')
                ->danger()
                ->send();
        }
    }

    protected function getFilteredQuery(Builder $query): Builder
    {
        // Get form state with fallback to default values
        $data = $this->data ?? [
            'period' => 'total',
            'date_from' => null,
            'date_to' => null,
        ];

        // Merge with current form state if available
        try {
            $formState = $this->form->getState();
            $data = array_merge($data, $formState);
        } catch (\Exception) {
            // Use default data if form state is not available
        }

        if ($data['period'] !== 'total' && isset($data['date_from']) && isset($data['date_to']) && $data['date_from'] && $data['date_to']) {
            $query->whereBetween('created_at', [
                Carbon::parse($data['date_from'])->startOfDay(),
                Carbon::parse($data['date_to'])->endOfDay(),
            ]);
        }

        return $query;
    }

    public function getTripsData(): array
    {
        // Return cached report data if available
        if ($this->reportData !== null) {
            return $this->reportData;
        }

        // Fallback to default data if no report has been generated
        return [
            'total_trips' => 0,
            'total_revenue' => 0,
            'status_distribution' => [],
            'revenue_by_status' => [],
            'period_info' => 'No report generated yet. Please click "Generate Report" to view data.',
        ];
    }

    protected function calculateTripsData(array $data): array
    {
        $query = Trip::query();

        // Apply date filters
        if ($data['period'] !== 'total' && isset($data['date_from']) && isset($data['date_to']) && $data['date_from'] && $data['date_to']) {
            $query->whereBetween('created_at', [
                Carbon::parse($data['date_from'])->startOfDay(),
                Carbon::parse($data['date_to'])->endOfDay(),
            ]);
        }

        $totalTrips = $query->count();

        // Get status distribution with revenue
        $statusDistribution = Trip::query()
            ->when($data['period'] !== 'total' && isset($data['date_from']) && isset($data['date_to']) && $data['date_from'] && $data['date_to'], function ($q) use ($data) {
                $q->whereBetween('created_at', [
                    Carbon::parse($data['date_from'])->startOfDay(),
                    Carbon::parse($data['date_to'])->endOfDay(),
                ]);
            })
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        // Calculate total revenue
        $revenueQuery = Trip::query()
            ->when($data['period'] !== 'total' && isset($data['date_from']) && isset($data['date_to']) && $data['date_from'] && $data['date_to'], function ($q) use ($data) {
                $q->whereBetween('created_at', [
                    Carbon::parse($data['date_from'])->startOfDay(),
                    Carbon::parse($data['date_to'])->endOfDay(),
                ]);
            });

        $totalRevenue = $this->calculateTotalRevenue($revenueQuery);

        // Calculate revenue by status
        $revenueByStatus = $this->calculateRevenueByStatus($revenueQuery);

        // Add period info
        $periodInfo = $this->getPeriodInfo($data);

        return [
            'total_trips' => $totalTrips,
            'total_revenue' => $totalRevenue,
            'status_distribution' => $statusDistribution,
            'revenue_by_status' => $revenueByStatus,
            'period_info' => $periodInfo,
        ];
    }

    protected function getPeriodInfo(array $data): string
    {
        if ($data['period'] === 'total') {
            return 'All time data';
        }

        if (! isset($data['date_from']) || ! isset($data['date_to']) || ! $data['date_from'] || ! $data['date_to']) {
            return 'No date range selected';
        }

        $from = Carbon::parse($data['date_from'])->format('M j, Y');
        $to = Carbon::parse($data['date_to'])->format('M j, Y');

        return "From {$from} to {$to}";
    }
}
