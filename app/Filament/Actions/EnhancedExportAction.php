<?php

namespace App\Filament\Actions;

use App\Services\ExportStatusService;
use Filament\Actions\ExportAction;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;

class EnhancedExportAction extends ExportAction
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->modalSubmitActionLabel('Start Export');
        
        $this->action(function (array $data) {
            // Get the query and count total rows
            $query = $this->getQuery();
            if ($this->modifyQueryUsing) {
                $query = $this->evaluate($this->modifyQueryUsing, ['query' => $query]) ?? $query;
            }
            
            $totalRows = $query->count();
            
            // Show loading notification
            Notification::make()
                ->title('Export Started')
                ->body("CSV export has been queued. Processing {$totalRows} records...")
                ->info()
                ->persistent()
                ->send();

            // Create the export record
            $export = $this->createExport($data);
            
            // Create export status tracking
            ExportStatusService::createStatus($export, $totalRows);
            
            // Dispatch the export job
            $this->dispatchExportJob($export);
            
            // Emit event for real-time tracking
            $this->getLivewire()->dispatch('export-started', exportId: $export->id);
            
            return $export;
        });
    }

    protected function createExport(array $data): \Filament\Actions\Exports\Models\Export
    {
        $export = new \Filament\Actions\Exports\Models\Export();
        $export->user_id = auth()->id();
        $export->exporter = $this->getExporter();
        $export->total_rows = 0; // Will be updated by the job
        $export->file_disk = config('filament.default_filesystem_disk');
        $export->save();

        return $export;
    }

    protected function dispatchExportJob(\Filament\Actions\Exports\Models\Export $export): void
    {
        $exporter = $this->getExporter();
        $query = $this->getQuery();
        
        if ($this->modifyQueryUsing) {
            $query = $this->evaluate($this->modifyQueryUsing, ['query' => $query]) ?? $query;
        }

        // Update total rows
        $export->update(['total_rows' => $query->count()]);

        // Dispatch the export job
        dispatch(new \App\Jobs\ProcessExport($export, $exporter, $query));
    }

    public static function make(?string $name = null): static
    {
        $static = app(static::class, ['name' => $name ?? 'export']);
        $static->configure();

        return $static;
    }
}
