<?php

namespace App\Filament\Exports;

use App\Services\ExportStatusService;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;
use Illuminate\Support\Facades\Log;

abstract class EnhancedExporter extends Exporter
{
    protected int $progressUpdateInterval = 100; // Update progress every 100 rows

    protected int $processedRowsCount = 0;

    /**
     * Override the export method to add progress tracking
     */
    public function export(Export $export, bool $sendCompletedNotification = true): void
    {
        try {
            // Mark export as started
            ExportStatusService::markAsStarted($export->id);
            
            Log::info("Starting enhanced export", [
                'export_id' => $export->id,
                'exporter' => static::class,
                'total_rows' => $export->total_rows,
            ]);

            // Call parent export method
            parent::export($export, false); // Disable default notification

            // Mark as completed if no exceptions occurred
            ExportStatusService::updateProgress(
                $export->id,
                $export->total_rows,
                'Export completed successfully'
            );

            Log::info("Enhanced export completed", [
                'export_id' => $export->id,
                'processed_rows' => $export->successful_rows,
            ]);

        } catch (\Exception $e) {
            Log::error("Enhanced export failed", [
                'export_id' => $export->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            ExportStatusService::markAsFailed(
                $export->id,
                'Export failed: ' . $e->getMessage()
            );

            throw $e;
        }
    }

    /**
     * Override the chunk processing to add progress updates
     */
    protected function processChunk($chunk, Export $export): void
    {
        parent::processChunk($chunk, $export);
        
        $this->processedRowsCount += count($chunk);

        // Update progress at intervals
        if ($this->processedRowsCount % $this->progressUpdateInterval === 0) {
            $this->updateProgress($export);
        }
    }

    /**
     * Update export progress
     */
    protected function updateProgress(Export $export): void
    {
        $message = $this->getProgressMessage();
        
        ExportStatusService::updateProgress(
            $export->id,
            $this->processedRowsCount,
            $message
        );

        Log::debug("Export progress updated", [
            'export_id' => $export->id,
            'processed_rows' => $this->processedRowsCount,
            'total_rows' => $export->total_rows,
            'message' => $message,
        ]);
    }

    /**
     * Get progress message based on current state
     */
    protected function getProgressMessage(): string
    {
        $percentage = $this->processedRowsCount > 0 && isset($this->export) 
            ? round(($this->processedRowsCount / $this->export->total_rows) * 100)
            : 0;

        return "Processing records... {$this->processedRowsCount} completed ({$percentage}%)";
    }

    /**
     * Get enhanced completion notification body
     */
    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Your enhanced export has completed and ' . 
                number_format($export->successful_rows) . ' ' . 
                str('row')->plural($export->successful_rows) . ' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . 
                     str('row')->plural($failedRowsCount) . ' failed to export.';
        }

        return $body;
    }

    /**
     * Set progress update interval
     */
    public function setProgressUpdateInterval(int $interval): static
    {
        $this->progressUpdateInterval = max(1, $interval);
        return $this;
    }
}
