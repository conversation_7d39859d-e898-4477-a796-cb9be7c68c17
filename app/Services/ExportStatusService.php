<?php

namespace App\Services;

use App\Models\ExportStatus;
use Filament\Actions\Exports\Models\Export;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ExportStatusService
{
    /**
     * Create a new export status record
     */
    public static function createStatus(Export $export, int $totalRows): ExportStatus
    {
        return ExportStatus::create([
            'export_id' => $export->id,
            'user_id' => Auth::id(),
            'status' => 'pending',
            'total_rows' => $totalRows,
            'status_message' => 'Export queued for processing...',
            'metadata' => [
                'exporter_class' => $export->exporter,
                'created_by' => Auth::user()->name ?? 'Unknown',
            ],
        ]);
    }

    /**
     * Get export status by export ID
     */
    public static function getStatus(int $exportId): ?ExportStatus
    {
        return ExportStatus::where('export_id', $exportId)
            ->where('user_id', Auth::id())
            ->first();
    }

    /**
     * Get all active export statuses for current user
     */
    public static function getActiveExports(): \Illuminate\Database\Eloquent\Collection
    {
        return ExportStatus::where('user_id', Auth::id())
            ->whereIn('status', ['pending', 'processing'])
            ->with('export')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Update export progress
     */
    public static function updateProgress(
        int $exportId, 
        int $processedRows, 
        ?string $message = null
    ): void {
        $status = self::getStatus($exportId);
        
        if (!$status) {
            Log::warning("Export status not found for export ID: {$exportId}");
            return;
        }

        $status->updateProgress($processedRows, $message);

        // Send real-time notification if completed
        if ($status->isCompleted()) {
            self::sendCompletionNotification($status);
        }
    }

    /**
     * Mark export as failed
     */
    public static function markAsFailed(int $exportId, string $errorMessage): void
    {
        $status = self::getStatus($exportId);
        
        if (!$status) {
            Log::warning("Export status not found for export ID: {$exportId}");
            return;
        }

        $status->markAsFailed($errorMessage);
        self::sendFailureNotification($status);
    }

    /**
     * Mark export as started
     */
    public static function markAsStarted(int $exportId): void
    {
        $status = self::getStatus($exportId);
        
        if (!$status) {
            Log::warning("Export status not found for export ID: {$exportId}");
            return;
        }

        $status->markAsStarted();
        self::sendStartNotification($status);
    }

    /**
     * Send initial export started notification
     */
    private static function sendStartNotification(ExportStatus $status): void
    {
        Notification::make()
            ->title('Export Started')
            ->body("CSV export processing has begun. Processing {$status->total_rows} records...")
            ->info()
            ->persistent()
            ->send();
    }

    /**
     * Send export completion notification with download action
     */
    private static function sendCompletionNotification(ExportStatus $status): void
    {
        $export = $status->export;
        
        Notification::make()
            ->title('Export Complete')
            ->body("Your CSV export has completed successfully! {$status->processed_rows} records exported.")
            ->success()
            ->persistent()
            ->actions([
                \Filament\Notifications\Actions\Action::make('download')
                    ->label('Download CSV')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->url(route('filament.admin.exports.download', $export))
                    ->openUrlInNewTab(),
            ])
            ->send();
    }

    /**
     * Send export failure notification
     */
    private static function sendFailureNotification(ExportStatus $status): void
    {
        Notification::make()
            ->title('Export Failed')
            ->body("CSV export failed: {$status->status_message}")
            ->danger()
            ->persistent()
            ->send();
    }

    /**
     * Clean up old export statuses (older than 7 days)
     */
    public static function cleanupOldStatuses(): int
    {
        return ExportStatus::where('created_at', '<', now()->subDays(7))
            ->delete();
    }

    /**
     * Get export statistics for dashboard
     */
    public static function getExportStats(): array
    {
        $userId = Auth::id();
        
        return [
            'total_exports' => ExportStatus::where('user_id', $userId)->count(),
            'completed_exports' => ExportStatus::where('user_id', $userId)
                ->where('status', 'completed')->count(),
            'failed_exports' => ExportStatus::where('user_id', $userId)
                ->where('status', 'failed')->count(),
            'active_exports' => ExportStatus::where('user_id', $userId)
                ->whereIn('status', ['pending', 'processing'])->count(),
        ];
    }
}
