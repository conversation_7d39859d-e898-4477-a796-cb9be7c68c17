<div x-data="exportStatusTracker()" 
     x-show="showTracker" 
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0 transform translate-y-2"
     x-transition:enter-end="opacity-100 transform translate-y-0"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100 transform translate-y-0"
     x-transition:leave-end="opacity-0 transform translate-y-2"
     class="fixed bottom-4 right-4 z-50 max-w-sm w-full">
    
    @if($showTracker && count($activeExports) > 0)
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
            <!-- Header -->
            <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                <div class="flex items-center justify-between">
                    <h3 class="text-sm font-medium text-gray-900 dark:text-white flex items-center">
                        <x-heroicon-o-arrow-down-tray class="w-4 h-4 mr-2" />
                        Export Progress
                    </h3>
                    <button @click="$wire.showTracker = false" 
                            class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <x-heroicon-o-x-mark class="w-4 h-4" />
                    </button>
                </div>
            </div>

            <!-- Export Items -->
            <div class="max-h-96 overflow-y-auto">
                @foreach($activeExports as $export)
                    <div class="px-4 py-3 border-b border-gray-100 dark:border-gray-600 last:border-b-0">
                        <div class="flex items-start justify-between mb-2">
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                    CSV Export #{{ $export['export_id'] }}
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    Started at {{ $export['created_at'] }}
                                </p>
                            </div>
                            <button wire:click="dismissExport({{ $export['export_id'] }})" 
                                    class="ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                                <x-heroicon-o-x-mark class="w-3 h-3" />
                            </button>
                        </div>

                        <!-- Progress Bar -->
                        <div class="mb-2">
                            <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                <span>{{ number_format($export['processed_rows']) }} / {{ number_format($export['total_rows']) }} records</span>
                                <span>{{ $export['formatted_progress'] }}</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out" 
                                     style="width: {{ $export['progress_percentage'] }}%"></div>
                            </div>
                        </div>

                        <!-- Status Message -->
                        <p class="text-xs text-gray-500 dark:text-gray-400 mb-1">
                            {{ $export['status_message'] }}
                        </p>

                        <!-- Estimated Completion -->
                        @if($export['estimated_completion'])
                            <p class="text-xs text-blue-600 dark:text-blue-400">
                                {{ $export['estimated_completion'] }}
                            </p>
                        @endif

                        <!-- Status Badge -->
                        <div class="mt-2">
                            @if($export['status'] === 'processing')
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                    <svg class="animate-spin -ml-1 mr-1 h-3 w-3 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Processing
                                </span>
                            @elseif($export['status'] === 'pending')
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                    <x-heroicon-o-clock class="w-3 h-3 mr-1" />
                                    Pending
                                </span>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif
</div>

<script>
function exportStatusTracker() {
    return {
        showTracker: @entangle('showTracker'),
        pollingInterval: null,
        
        init() {
            this.$wire.on('export-status-updated', (data) => {
                this.showTracker = data.showTracker;
            });

            this.$wire.on('start-polling', () => {
                this.startPolling();
            });

            this.$wire.on('stop-polling', () => {
                this.stopPolling();
            });

            // Start polling if there are active exports
            if (this.showTracker) {
                this.startPolling();
            }
        },

        startPolling() {
            if (this.pollingInterval) return;
            
            this.pollingInterval = setInterval(() => {
                this.$wire.loadActiveExports();
            }, {{ $pollingInterval }});
        },

        stopPolling() {
            if (this.pollingInterval) {
                clearInterval(this.pollingInterval);
                this.pollingInterval = null;
            }
        },

        destroy() {
            this.stopPolling();
        }
    }
}
</script>
